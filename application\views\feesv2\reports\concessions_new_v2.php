<?php include(APPPATH . 'views/feesv2/reports/components/report_header.php'); ?>
    <div class="card-body pt-1">
      
      <div class="col-md-12">
        <div class="row">
          <div class="col-md-3 form-group" id="multiBlueprintSelect">
            <p style="margin-bottom: -1px;">Select Fee Type <font color="red">*</font></p>
            <select class="form-control select" title="All" id="blueprint_type" required="" name="fee_type[]" multiple>
              <?php foreach ($fee_blueprints as $key => $val) { ?>
                <option <?php if($fee_type == $val->id) echo "selected"; ?> value="<?= $val->id ?>"><?php echo $val->name?></option>
              <?php } ?>
            </select>
          </div>


          <div class="col-md-3 form-group">
            <p style="margin-bottom: -1px;">Class</p>
            <?php 
                $array = array();
                foreach ($classes as $key => $class) {
                  $array[$class->classId] = $class->className; 
                }
            echo form_dropdown("class_name[]", $array, set_value("class_name"), "id='classId' multiple title='All' class='form-control classId select '");
            ?>
          </div>


           <div class="col-md-2 form-group">
            <p style="margin-bottom: -1px;">Select Class/Sections</p>
              <?php 
                $array = array();
                // $array[0] = 'All Section';
                foreach ($classSectionList as $key => $cl) {
                    $array[$cl->id] = $cl->class_name . $cl->section_name;
                }
                echo form_dropdown("classSectionId", $array, '', "id='classSectionId' multiple title='All' class='form-control select'");
              ?>
            </div>

            <div class="col-md-2 form-group">
              <input type="button" value="Get Report" onclick="get_concession_report()" id="search" class="btn btn-primary" style="margin-top: 17px;">
            </div>
        </div>
          <div class="col-12 text-center loading-icon" style="display: none;">
            <i class="fa fa-spinner fa-spin" style="font-size: 40px;"></i>
          </div>
      </div>
      
      <div class="col-md-12 pt-2" style="overflow: hidden;">

        <div class="text-center"><div style="display: none;" class="progress" id="progress"><div id="progress-ind" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" ariavaluenow="50" aria-valuemin="0" aria-valuemax="100" style="width: 50%"></div></div></div>
        <div class="total_summary">
        </div>

        <div id="fees_student_status" class="fee_balance pt-3 table-responsive">
          
        </div>

      </div>
    </div>


  </div> 
</div>

<script type="text/javascript">
  
  function exportToExcel_fee_status(){
    var htmls = "";
    var uri = 'data:application/vnd.ms-excel;base64,';
    var template = '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40"><head><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>{worksheet}</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--><meta http-equiv="content-type" content="text/plain; charset=UTF-8"/></head><body><table>{table}</table></body></html>';
    var base64 = function(s) {
        return window.btoa(unescape(encodeURIComponent(s)))
    };

    var format = function(s, c) {
        return s.replace(/{(\w+)}/g, function(m, p) {
            return c[p];
        })
    };

    var mainTable = $("#fee_summary_data").clone();
    var summaryTable = $(".total_summary").clone();
    mainTable.find(".dt-buttons").remove();
    summaryTable.find(".distrubanceAmount").remove();
    
    var titleRow = '';
    htmls = titleRow + summaryTable.prop("outerHTML") + "<br>" +mainTable.prop("outerHTML");
    var ctx = { worksheet: "Spreadsheet", table: htmls };
    if (navigator.msSaveOrOpenBlob) {
        var blob = new Blob([format(template, ctx)], { type: "application/vnd.ms-excel" });
        navigator.msSaveOrOpenBlob(blob, "fees_concessions_report.xls");
    } else {
        var link = document.createElement("a");
        link.download = "fees_concessions_report.xls";
        link.href = uri + base64(format(template, ctx));
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

  }
</script>
<style type="text/css">
    @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500&display=swap');

    table {
        font-family: 'Poppins', sans-serif !important;
    }
    #fee_summary_data,
    #total_summary {
        width: 100%;
        border-collapse: collapse;
        background-color: #ffffff;
        border-radius: 1.5rem;
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
        opacity: 1 !important;
        transition: none !important;
    }

    #fee_summary_data thead th,
    #total_summary thead th {
        position: sticky !important;
        top: 0;
        background-color: #f1f5f9;
        color: #111827;
        font-size: 11px;
        font-weight: 500;
        z-index: 10;
        text-align: left;
        padding: 12px 16px;
    }

    #fee_summary_data th,
    #fee_summary_data td,
    #total_summary th,
    #total_summary td {
        padding: 10px 14px;
        border-bottom: 1px solid #e5e7eb;
        font-size: 11px;
        font-weight: 400;
    }

    #fee_summary_data tbody tr:nth-child(even) {
        background-color: #f9fafb;
    }

    #fee_summary_data tbody tr:hover,
    #total_summary tbody tr:hover {
        background-color: #f1f5f9;
    }

    #fee_summary_data tfoot tr,
    #total_summary tfoot tr {
        background-color: #f3f4f6;
        font-weight: 500;
    }

    .fee_summary_data {
        margin: 12px 0;
        overflow-x: auto;
        max-height: 500px;
        scrollbar-width: thick; /* For Firefox */
        scrollbar-color: #cbd5e1 #f1f5f9; /* For Firefox */
    }
    .fee_summary_data::-webkit-scrollbar {
        height: 16px;
        width: 16px;
    }
    .fee_summary_data::-webkit-scrollbar-thumb {
        background: #cbd5e1;
        border-radius: 8px;
    }
    .fee_summary_data::-webkit-scrollbar-track {
        background: #f1f5f9;
        border-radius: 8px;
    }
    #fee_summary_data thead th {
        position: sticky !important;
        top: 0;
        background-color: #f1f5f9;
        z-index: 2;
    }
    #table-toolbar {
        position: static !important;
        background: none !important;
        z-index: 1;
    }
    /* For Firefox */
    .fee_summary_data {
        scrollbar-width: thick;
        scrollbar-color: #cbd5e1 #f1f5f9;
    }
</style>

<style>
  .buttons-print{
    padding: 2px  !important;
}
.buttons-colvis{
    padding: 2px !important;
}

.buttons-excel{
  border: none !important;
  background: none  !important;
  padding: 0  !important;
  margin: 0  !important;
}
.dt-button{
  border: none !important;
  background: none  !important;
}
.btn-info{
    border-radius: 8px !important; 
}
.dt-button .btn{
  line-height:20px;
}
.dt-buttons{
  text-align: right;
  float:right;
  /*position: absolute;*/
}
.dataTables_scrollHeadInner table{
  margin:0px;
}
  .dt-button-collection .buttons-columnVisibility:before {
  content: ' ';
  margin-top: -6px;
  margin-left: 10px;
  border: 1px solid black;
  border-radius: 3px;
}

.dt-button-collection .buttons-columnVisibility:before, .dt-button-collection .buttons-columnVisibility.active span:before {
    display: block;
    position: absolute;
    top: 1.2em;
    left: 0;
    width: 12px;
    height: 12px;
    box-sizing: border-box;
}

.dt-button-collection .buttons-columnVisibility span {
  margin-left: 20px;
}

.dt-button-collection .buttons-columnVisibility.active span:before {
  content: '\2714'; /* Unicode checkmark character */
  margin-top: -10px;
  margin-left: 10px;
  text-align: center;
  text-shadow: 1px 1px #DDD, -1px -1px #DDD, 1px -1px #DDD, -1px 1px #DDD;
}

div.dt-button-collection .dt-button {
    position: relative;
    left: 0;
    right: 0;
    width: 100%;
    display: block;
    float: none;
    background: none;
    margin: 0;
    padding: .5em 1em;
    border: none;
    text-align: left;
    cursor: pointer;
    color: inherit;
}

#fee_summary_data_filter .dt-button-collection{
    height: 300px;
    overflow: scroll;
  }

div.dt-button-collection {
    position: absolute;
    top: 0;
    left: 0;
    width: 200px;
    margin-top: 3px;
    margin-bottom: 3px;
    padding: .75em 0;
    border: 1px solid rgba(0, 0, 0, 0.4);
    background-color: white;
    overflow: hidden;
    z-index: 2002;
    border-radius: 5px;
    box-shadow: 3px 4px 10px 1px rgba(0, 0, 0, 0.3);
    box-sizing: border-box;
}


.search-box {
        display: inline-block;
        position: relative;
        margin-right: 2px;
        vertical-align: middle;
    }

    .input-search {
        line-height: 1.5;
        padding: 5px 10px;
        display: inline;
        width: 177px;
        height: 27px;
        background-color: #f2f2f2 !important;
        border: 1px solid #ccc !important;
        border-radius: 4px !important;
        margin-right: 0 !important;
        font-size: 14px;
        color: #495057;
        outline: none;
    }

    .input-search::placeholder {
        color: rgba(73, 80, 87, 0.5);
        font-size: 14px;
        font-weight: 300;
    }

  </style>
<script>
  $(document).ready(function(){
    var fee_type = $('#blueprint_type').val();
    console.log(fee_type);
    // if (fee_type?.length) {
      // get_concession_report();
    // }
  });
  var concession_total = 0;
  var concession_total_applied = 0;
  var concession_total_pending = 0;
  var completed = 0;
  var total_students =0;
  var total_additional_amount_paid = 0;
  var is_enabled_additionConcession = '<?php echo $this->settings->getSetting('addition_concession_amount') ?>';
  function get_concession_report() {
      var fee_type = $('#blueprint_type').val();
      // if (!fee_type?.length) {
      //   return false;
      // }
      $('.loading-icon').show();
      concession_total = 0;
      concession_total_applied = 0;
      concession_total_pending = 0;
      total_additional_amount_paid = 0;
      total_students = 0;
      completed = 0;
      $('.fee_balance').html('');
      $('.total_summary').html('');
      var classSectionId =  $('#classSectionId').val();
      var classId =  $('#classId').val();

      $.ajax({
        url: '<?php echo site_url('feesv2/reports/get_fee_concession_student_count'); ?>',
        data: {'fee_type':fee_type,'classSectionId':classSectionId,'classId':classId},
        type: "post",
        success: function (data) {
          $('.loading-icon').hide();
          var data = JSON.parse(data);
          var students = data;
          studentIds = students;
          total_students = parseInt(100* (studentIds.length - 2)) + parseInt(studentIds[studentIds.length - 1].length);
          var progress = document.getElementById('progress-ind');
          progress.style.width = (completed/total_students)*100+'%';
          $("#progress").show();
          $('#search').prop('disabled', true).val('Please wait...');
          callReportGetter(0);
        },
        error: function (err) {
          console.log(err);
        }
      });
  }
  var global_students_data = [];
  function getReport(index) {
    var student_ids = studentIds[index];
    var classSectionId =  $('#classSectionId').val();
    var classId =  $('#classId').val();
    var fee_type = $('#blueprint_type').val();
    $.ajax({
      url: '<?php echo site_url('feesv2/reports/get_fee_concession_details'); ?>',
      data: {student_ids:student_ids, 'fee_type':fee_type,'classSectionId':classSectionId,'classId':classId},
      type: "post",
      success: function (data) {
        if(data.length==0){
          return html=`<h4>No Data</h4>`;
        }
        var data = JSON.parse(data);
        var concessionStudent = data;

        if (index == 0) {
            global_students_data = [];
        }
        global_students_data = global_students_data.concat(concessionStudent);

        construct_balance_summary(concessionStudent);
        completed += Object.keys(concessionStudent).length;
        var progress = document.getElementById('progress-ind');
        progress.style.width = (completed/total_students)*100+'%';

        if(index == studentIds.length - 1) {
          constructConcessionHeader();
          construct_concession_table(0, global_students_data);
        }
        index++;
        callReportGetter(index);

       
      },
      error: function (err) {
        console.log(err);
      }
    });
  }

  
  function constructConcessionHeader(header) {

  var h_output = `
  <div class="d-flex align-items-center mb-2 justify-content-end" id="table-toolbar">
    <div class="search-box" style="margin-bottom:0;">
      <input type="text" class="input-search" id="table-search" placeholder="Enter Search...">
    </div>
    <div id="dt-buttons-container" style="margin-left:8px;"></div>
  </div>
  <div class="fee_summary_data">
    <table id="fee_summary_data" class="table table-bordered" style="white-space: nowrap">
      <thead>
        <tr>
          <th>#</th>
          <th>Admission No.</th>
          <th>Enrollment No.</th>
          <th>Fee Type</th>
          <th>Student Name</th>
          <th>Class Section</th>
          <th>Category</th>
          <th>Caste</th>
          <th>Total Concession</th>
          <th>Concession Applied</th>
          <th>Concession Pending</th>
          ${is_enabled_additionConcession == 1 ? '<th>Additional amount</th>' : ''}
          <th>Remarks</th>
        </tr>
      </thead>
    </table>
  </div>
`;
$('.fee_balance').html(h_output);
  }

  function construct_concession_table(index, concessionData) {
    //console.log(concessionData);
    var srNo = index * 100;
    html = '<tbody>';
    var j=0;
    var total_con = 0;
    for(var i in concessionData) {
      total_con =  parseFloat(concessionData[i].total_concession_amount) + parseFloat(concessionData[i].total_concession_amount_paid);
      if(total_con != 0){
        html += '<tr>';
        html += '<td>'+(j+1+srNo)+'</td>';
        html += '<td>'+concessionData[i].admission_no+'</td>';
        html += '<td>'+concessionData[i].enrollment_number+'</td>';
        html += '<td>'+concessionData[i].fee_type_name+'</td>';
        html += '<td>'+concessionData[i].stdName+'</td>';
        html += '<td>'+concessionData[i].class_name+'</td>';
        html += '<td>'+concessionData[i].category+'</td>';
        html += '<td>'+concessionData[i].caste+'</td>';
        html += '<td>'+numberToCurrency(total_con)+'</td>';
        html += '<td>'+numberToCurrency(concessionData[i].total_concession_amount_paid)+'</td>';
        html += '<td>'+numberToCurrency(concessionData[i].total_concession_amount)+'</td>';
        if (is_enabled_additionConcession == 1) {
          html += '<td>'+numberToCurrency(concessionData[i].additional_amount_paid)+'</td>';
        }
        html += '<td>'+concessionData[i].concession_remarks+'</td>';
        html += '</tr>';
        j++;
      }
    }
    html += '</tbody>';
    $('#fee_summary_data').append(html);
    // index++;
    // callReportGetter(index);
  }
  $(document).on('keyup', '#table-search', function() {
    const searchText = $(this).val().toLowerCase();

    $('#fee_summary_data tbody tr').each(function() {
        let rowVisible = false;

        $(this).find('td').each(function() {
            if ($(this).text().toLowerCase().indexOf(searchText) > -1) {
                rowVisible = true;
                return false; // Break loop if found
            }
        });

        $(this).toggle(rowVisible);
    });
});
  function construct_balance_summary(concessionStudent) {
    for(key in concessionStudent){
      concession_total += parseFloat(concessionStudent[key].total_concession_amount) + parseFloat(concessionStudent[key].total_concession_amount_paid);
      concession_total_applied += parseFloat(concessionStudent[key].total_concession_amount_paid);
      concession_total_pending += parseFloat(concessionStudent[key].total_concession_amount);
      total_additional_amount_paid += parseFloat(concessionStudent[key].additional_amount_paid);
    }
  }
  function callReportGetter(index){
    if(index < studentIds.length) {
      getReport(index);
    } else {
      $("#progress").hide();
      $('#search').prop('disabled', false).val('Get Report');
      $('#exportIcon').show();
      $('#send_fee_sms').show();
      con_summary = '<div class="table-responsive">';
      con_summary +='<table class="table table-bordered">';
      con_summary +='<thead>';
      con_summary +='<tr>';
      con_summary +='<th>Total Concession</th>';     
      con_summary +='<th>Concession Applied</th>';     
      con_summary +='<th>Concession Pending</th>'; 
      if (is_enabled_additionConcession == 1) {
        con_summary +='<th>Total Additional Amounts</th>';
      }    
      con_summary +='</tr>';
      con_summary +='</thead>';
      con_summary +='<tbody>';
      con_summary += '<tr>';
      con_summary +='<th>'+numberToCurrency(concession_total)+'</th>';
      con_summary +='<th>'+numberToCurrency(concession_total_applied)+'</th>';
      con_summary +='<th>'+numberToCurrency(concession_total_pending)+'</th>';
      if (is_enabled_additionConcession == 1) {
        con_summary +='<th>'+numberToCurrency(total_additional_amount_paid)+'</th>';
      }
      con_summary += '</tr>';
      con_summary +='</tbody>';
      con_summary +='</table>';
      con_summary += '</div>';
      $(".total_summary").html(con_summary);

      let table = $('#fee_summary_data').DataTable({
        ordering: false,
        paging: false,
        searching: false, 
        dom: 'Bfrtip', 
        info: false,
        'columnDefs':  [
          { orderable: false, targets: 1 },
          {targets : 6, visible: false},
          {targets : 7, visible: false},
        ],
        buttons: [
          { extend: 'colvis',
             text: '<button class="btn btn-info"><span class="fa fa-columns" aria-hidden="true"></span> Columns</button>'
          },
          {
            text: '<button class="btn btn-info"><span class="fa fa-print" aria-hidden="true"></span> Print</button>',
            action: function () {
              customPrintConcession();
            }
          },
          {
            text: '<button class="btn btn-info"><span class="fa fa-file-excel-o" aria-hidden="true"></span> Excel</button>',
            title: 'Fee Concessions report',
            action: function () {
              exportToExcel_fee_status();
            },
          },
        ],
      });
      table.buttons().container().appendTo('#dt-buttons-container');
      

    }
  }

  function customPrintConcession() {
    const printWindow = window.open('', '_blank');
    // You can adjust these selectors as needed
    let printHeader = document.querySelector('.panel_title_new_style_staff')?.outerHTML || '';
    let summaryTable = document.querySelector('.total_summary')?.innerHTML || '';
    let componentTable = document.getElementById('fee_summary_data')?.outerHTML || '';

    printWindow.document.write(`
        <html>
        <head>
            <title>Fee Concessions Report</title>
            <style>
                body {
                    font-family: 'Poppins', sans-serif;
                    padding: 20px;
                }
                h3, h4, h5 {
                    margin: 5px 0;
                    text-align: center;
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin: 15px 0;
                    font-size: 11px;
                    background-color: #ffffff;
                    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
                }
                th, td {
                    border: 1px solid #ddd;
                    padding: 10px 14px;
                    text-align: left;
                }
                thead th {
                    background-color: #f1f5f9;
                    color: #111827;
                    font-weight: 500;
                }
                #print_visible {
                    display: block !important;
                }
                @media print {
                    table { page-break-inside: auto; }
                    tr { page-break-inside: avoid; }
                }
            </style>
        </head>
        <body>
            ${printHeader}
            <h3>Fee Summary</h3>
            ${summaryTable}
            <h3>Concession Details</h3>
            ${componentTable}
            <script>
            window.onload = function() {
                window.print();
            };
            window.onafterprint = function() {
                window.close();
            };
            <\/script>
        </body>
        </html>
    `);

    printWindow.document.close();
    printWindow.print();
}


  function initializeColVisButton(table, wrapperId) {
    // Add a column visibility button to the given table
    new $.fn.dataTable.Buttons(table, {
      buttons: [
        {
          extend: 'colvis',
          text: '<button class="btn btn-info"><span class="fa fa-columns" aria-hidden="true"></span> Columns</button>',
          className: 'btn btn-info',
        },
      ],
    }).container().appendTo($(`#${wrapperId} label`));
  }
</script>
<script type="text/javascript">
  function numberToCurrency(amount) {
    var formatter = new Intl.NumberFormat('en-IN', {
      // style: 'currency',
      currency: 'INR',
    });
    return formatter.format(amount);
  }
</script>