<?php include(APPPATH . 'views/feesv2/reports/components/report_header.php'); ?>

<?php $this->load->helper('reports_datatable');
echo progress_bar(); ?>

<?php $this->load->helper('reports_datatable'); ?>
<div id="no-data-template" style="display: none;">
    <?php echo no_data_message(); ?>
</div>

    <div class="card-body pt-1">
      
      <div class="col-md-12">
        <div class="row">
          <div class="col-md-3 form-group" id="multiBlueprintSelect">
            <p style="margin-bottom: -1px;">Select Fee Type <font color="red">*</font></p>
            <select class="form-control select" title="All" id="blueprint_type" required="" name="fee_type[]" multiple>
              <?php foreach ($fee_blueprints as $key => $val) { ?>
                <option <?php if($fee_type == $val->id) echo "selected"; ?> value="<?= $val->id ?>"><?php echo $val->name?></option>
              <?php } ?>
            </select>
          </div>


          <div class="col-md-3 form-group">
            <p style="margin-bottom: -1px;">Class</p>
            <?php 
                $array = array();
                foreach ($classes as $key => $class) {
                  $array[$class->classId] = $class->className; 
                }
            echo form_dropdown("class_name[]", $array, set_value("class_name"), "id='classId' multiple title='All' class='form-control classId select '");
            ?>
          </div>


           <div class="col-md-2 form-group">
            <p style="margin-bottom: -1px;">Select Class/Sections</p>
              <?php 
                $array = array();
                // $array[0] = 'All Section';
                foreach ($classSectionList as $key => $cl) {
                    $array[$cl->id] = $cl->class_name . $cl->section_name;
                }
                echo form_dropdown("classSectionId", $array, '', "id='classSectionId' multiple title='All' class='form-control select'");
              ?>
            </div>

            <div class="col-md-2 form-group">
              <input type="button" value="Get Report" onclick="get_concession_report()" id="search" class="btn btn-primary" style="margin-top: 17px;">
            </div>
        </div>
          <div class="col-12 text-center loading-icon" style="display: none;">
            <i class="fa fa-spinner fa-spin" style="font-size: 40px;"></i>
          </div>
      </div>
      
      <div class="col-md-12 pt-2" style="overflow: hidden;">

        <?php $this->load->helper('reports_datatable');
        echo progress_bar(); ?>
        <div class="total_summary">
        </div>

        <div id="range-input"></div>

        <div id="fees_student_status" class="fee_balance pt-3 table-responsive">

        </div>

      </div>
    </div>

</div>

<div id="printArea">
    <div id="print_visible" style="display: none;" class="text-center">
        <h3 style="margin-bottom: 0.25rem; font-size: 1.5rem; font-family: 'Poppins', serif;">
            <?php echo htmlspecialchars($this->settings->getSetting('school_name')); ?>
        </h3>
        <h4 style="margin-top: 0.25rem;font-size: 1.3rem;font-weight: bold;letter-spacing: 1px;color: #222;text-transform: uppercase;border-top: 1px solid #444;border-bottom: 1px solid #444;padding: 0.5rem 0;font-family: 'Poppins', serif;">
            Fee Concessions Report
        </h4>
    </div>
</div>

  </div>
</div>

<script type="text/javascript">
  
  function exportToExcel_fee_status(){
    var htmls = "";
    var uri = 'data:application/vnd.ms-excel;base64,';
    var template = '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40"><head><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>{worksheet}</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--><meta http-equiv="content-type" content="text/plain; charset=UTF-8"/></head><body><table>{table}</table></body></html>';
    var base64 = function(s) {
        return window.btoa(unescape(encodeURIComponent(s)))
    };

    var format = function(s, c) {
        return s.replace(/{(\w+)}/g, function(m, p) {
            return c[p];
        })
    };

    var mainTable = $("#fee_summary_data").clone();
    var summaryTable = $(".total_summary").clone();
    mainTable.find(".dt-buttons").remove();
    summaryTable.find(".distrubanceAmount").remove();
    
    var titleRow = '';
    htmls = titleRow + summaryTable.prop("outerHTML") + "<br>" +mainTable.prop("outerHTML");
    var ctx = { worksheet: "Spreadsheet", table: htmls };
    if (navigator.msSaveOrOpenBlob) {
        var blob = new Blob([format(template, ctx)], { type: "application/vnd.ms-excel" });
        navigator.msSaveOrOpenBlob(blob, "fees_concessions_report.xls");
    } else {
        var link = document.createElement("a");
        link.download = "fees_concessions_report.xls";
        link.href = uri + base64(format(template, ctx));
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

  }
</script>
<script>
  $(document).ready(function(){
    var fee_type = $('#blueprint_type').val();
    console.log(fee_type);
    // if (fee_type?.length) {
      // get_concession_report();
    // }
  });
  var concession_total = 0;
  var concession_total_applied = 0;
  var concession_total_pending = 0;
  var completed = 0;
  var total_students =0;
  var total_additional_amount_paid = 0;
  var is_enabled_additionConcession = '<?php echo $this->settings->getSetting('addition_concession_amount') ?>';
  function get_concession_report() {
      var fee_type = $('#blueprint_type').val();
      // if (!fee_type?.length) {
      //   return false;
      // }
      $('.loading-icon').show();
      concession_total = 0;
      concession_total_applied = 0;
      concession_total_pending = 0;
      total_additional_amount_paid = 0;
      total_students = 0;
      completed = 0;
      $('.fee_balance').html('');
      $('.total_summary').html('');
      var classSectionId =  $('#classSectionId').val();
      var classId =  $('#classId').val();

      $.ajax({
        url: '<?php echo site_url('feesv2/reports/get_fee_concession_student_count'); ?>',
        data: {'fee_type':fee_type,'classSectionId':classSectionId,'classId':classId},
        type: "post",
        success: function (data) {
          $('.loading-icon').hide();
          var data = JSON.parse(data);
          var students = data;
          studentIds = students;
          total_students = parseInt(100* (studentIds.length - 2)) + parseInt(studentIds[studentIds.length - 1].length);
          var progress = document.getElementById('progress-ind');
          progress.style.width = (completed/total_students)*100+'%';
          $("#progress").show();
          $('#search').prop('disabled', true).val('Please wait...');
          callReportGetter(0);
        },
        error: function (err) {
          console.log(err);
        }
      });
  }
  var global_students_data = [];
  function getReport(index) {
    var student_ids = studentIds[index];
    var classSectionId =  $('#classSectionId').val();
    var classId =  $('#classId').val();
    var fee_type = $('#blueprint_type').val();
    $.ajax({
      url: '<?php echo site_url('feesv2/reports/get_fee_concession_details'); ?>',
      data: {student_ids:student_ids, 'fee_type':fee_type,'classSectionId':classSectionId,'classId':classId},
      type: "post",
      success: function (data) {
        if(data.length==0){
          return html=`<h4>No Data</h4>`;
        }
        var data = JSON.parse(data);
        var concessionStudent = data;

        if (index == 0) {
            global_students_data = [];
        }
        global_students_data = global_students_data.concat(concessionStudent);

        construct_balance_summary(concessionStudent);
        completed += Object.keys(concessionStudent).length;
        var progress = document.getElementById('progress-ind');
        progress.style.width = (completed/total_students)*100+'%';

        if(index == studentIds.length - 1) {
          constructConcessionHeader();
          construct_concession_table(0, global_students_data);
        }
        index++;
        callReportGetter(index);

       
      },
      error: function (err) {
        console.log(err);
      }
    });
  }

  
  function constructConcessionHeader(header) {
    var h_output = '<div class="table-responsive" id="report_width_container">';
    h_output += '<table id="fee_summary_data" class="table table-bordered" style="width: 100%">';
    h_output += '<thead>';
    h_output += '<tr>';
    h_output += '<th>#</th>';
    h_output += '<th>Admission No.</th>';
    h_output += '<th>Enrollment No.</th>';
    h_output += '<th>Fee Type</th>';
    h_output += '<th>Student Name</th>';
    h_output += '<th>Class Section</th>';
    h_output += '<th>Category</th>';
    h_output += '<th>Caste</th>';
    h_output += '<th>Total Concession</th>';
    h_output += '<th>Concession Applied</th>';
    h_output += '<th>Concession Pending</th>';
    if (is_enabled_additionConcession == 1) {
      h_output += '<th>Additional amount</th>';
    }
    h_output += '<th>Remarks</th>';
    h_output += '</tr>';
    h_output += '</thead>';
    h_output += '</table>';
    h_output += '</div>';
    $('.fee_balance').html(h_output);
    add_scroller('report_width_container');
  }

  function construct_concession_table(index, concessionData) {
    //console.log(concessionData);
    var srNo = index * 100;
    html = '<tbody>';
    var j=0;
    var total_con = 0;
    for(var i in concessionData) {
      total_con =  parseFloat(concessionData[i].total_concession_amount) + parseFloat(concessionData[i].total_concession_amount_paid);
      if(total_con != 0){
        html += '<tr>';
        html += '<td>'+(j+1+srNo)+'</td>';
        html += '<td>'+concessionData[i].admission_no+'</td>';
        html += '<td>'+concessionData[i].enrollment_number+'</td>';
        html += '<td>'+concessionData[i].fee_type_name+'</td>';
        html += '<td>'+concessionData[i].stdName+'</td>';
        html += '<td>'+concessionData[i].class_name+'</td>';
        html += '<td>'+concessionData[i].category+'</td>';
        html += '<td>'+concessionData[i].caste+'</td>';
        html += '<td>'+numberToCurrency(total_con)+'</td>';
        html += '<td>'+numberToCurrency(concessionData[i].total_concession_amount_paid)+'</td>';
        html += '<td>'+numberToCurrency(concessionData[i].total_concession_amount)+'</td>';
        if (is_enabled_additionConcession == 1) {
          html += '<td>'+numberToCurrency(concessionData[i].additional_amount_paid)+'</td>';
        }
        html += '<td>'+concessionData[i].concession_remarks+'</td>';
        html += '</tr>';
        j++;
      }
    }
    html += '</tbody>';
    $('#fee_summary_data').append(html);
    // index++;
    // callReportGetter(index);
  }

  function construct_balance_summary(concessionStudent) {
    for(key in concessionStudent){
      concession_total += parseFloat(concessionStudent[key].total_concession_amount) + parseFloat(concessionStudent[key].total_concession_amount_paid);
      concession_total_applied += parseFloat(concessionStudent[key].total_concession_amount_paid);
      concession_total_pending += parseFloat(concessionStudent[key].total_concession_amount);
      total_additional_amount_paid += parseFloat(concessionStudent[key].additional_amount_paid);
    }
  }
  function callReportGetter(index){
    if(index < studentIds.length) {
      getReport(index);
    } else {
      $("#progress").hide();
      $('#search').prop('disabled', false).val('Get Report');
      $('#exportIcon').show();
      $('#send_fee_sms').show();
      con_summary = '<div class="table-responsive">';
      con_summary +='<table class="table table-bordered">';
      con_summary +='<thead>';
      con_summary +='<tr>';
      con_summary +='<th>Total Concession</th>';     
      con_summary +='<th>Concession Applied</th>';     
      con_summary +='<th>Concession Pending</th>'; 
      if (is_enabled_additionConcession == 1) {
        con_summary +='<th>Total Additional Amounts</th>';
      }    
      con_summary +='</tr>';
      con_summary +='</thead>';
      con_summary +='<tbody>';
      con_summary += '<tr>';
      con_summary +='<th>'+numberToCurrency(concession_total)+'</th>';
      con_summary +='<th>'+numberToCurrency(concession_total_applied)+'</th>';
      con_summary +='<th>'+numberToCurrency(concession_total_pending)+'</th>';
      if (is_enabled_additionConcession == 1) {
        con_summary +='<th>'+numberToCurrency(total_additional_amount_paid)+'</th>';
      }
      con_summary += '</tr>';
      con_summary +='</tbody>';
      con_summary +='</table>';
      con_summary += '</div>';
      $(".total_summary").html(con_summary);

      let table = $('#fee_summary_data').DataTable({
        ordering: false,
        searching: true,
        paging: true,
        pageLength: 10,
        pagingType: 'full_numbers',
        dom: '<"row align-items-center mb-2" <"col-md-6"l> <"col-md-6 d-flex justify-content-end gap-2"fB> >rt<"row align-items-center mt-4" <"col-md-6 mb-2"i> <"col-md-6 d-flex justify-content-end mb-2"p> >',
        info: true,
        'columnDefs': [{
            orderable: false,
            targets: 1
          },
          {
            targets: 6,
            visible: false
          },
          {
            targets: 7,
            visible: false
          },
        ],
        fixedHeader: true,
        scrollY: 400,
        scrollX: true,
        scrollCollapse: true,
        autoWidth: true,
        language: {
          search: "",
          searchPlaceholder: "Search",
          lengthMenu: "Show _MENU_ ",
          info: "Showing _START_ to _END_ of _TOTAL_ ",
          paginate: {
            first: "&laquo;",
            last: "&raquo;",
            next: "Next &rsaquo;",
            previous: "&lsaquo; Previous"
          },
          emptyTable: "No data available in table",
          zeroRecords: "No matching records found"
        },
        lengthMenu: [
          [10, 25, 50, -1],
          [10, 25, 50, "All"]
        ],
        buttons: [{
            extend: 'colvis',
            text: '<button class="btn btn-outline-primary" id="expbtns" style="margin-right:-7px;"><span class="fa fa-columns" aria-hidden="true"></span> Columns</button>',
            className: 'btn btn-outline-primary',
          },
          {
            extend: 'print',
            text: '<button class="btn btn-outline-primary" id="expbtns" style="margin-right:-7px;"><span class="fa fa-print" aria-hidden="true"></span> Print</button>',
            title: '',
            footer: true,
            exportOptions: {
              columns: ':visible',
            },
            customize: function(win) {
              $(win.document.body)
                .css('font-family', "'Poppins', sans-serif")
                .css('font-size', '10pt')
                .css('padding', '10px');

              // Add header from print_visible div
              var headerHtml = $('#print_visible').html();
              if (headerHtml) {
                $(win.document.body).prepend('<div class="text-center">' + headerHtml + '</div>');
              }

              $(win.document.head).append(`
                <style>
                  @page {
                    size: auto;
                    margin: 12mm;
                  }

                  body {
                    font-family: 'Poppins', sans-serif;
                    -webkit-print-color-adjust: exact;
                    print-color-adjust: exact;
                    color: #333;
                    background: #fff;
                  }

                  h2, h3 {
                    text-align: center;
                    margin-bottom: 15px;
                    font-weight: 500;
                  }

                  h4 {
                    text-align: center;
                    margin-top: 0.25rem;
                    font-size: 1.3rem;
                    font-weight: bold;
                    letter-spacing: 1px;
                    color: #222;
                    text-transform: uppercase;
                    border-top: 1px solid #444;
                    border-bottom: 1px solid #444;
                    padding: 0.5rem 0;
                    font-family: 'Poppins', serif;
                  }

                  table {
                    border-collapse: collapse !important;
                    width: 100% !important;
                    margin-top: 20px;
                    font-size: 10pt;
                    color: #333;
                  }

                  th, td {
                    border: 1px solid #ccc !important;
                    padding: 8px 12px;
                    text-align: left;
                    vertical-align: middle;
                  }

                  th {
                    background-color: #f4f7fc !important;
                    font-weight: 600;
                    color: #333;
                  }

                  .table-bordered {
                    width: 100% !important;
                  }

                  tfoot th {
                    background-color: #f9f9f9;
                    font-weight: 600;
                  }
                </style>
              `);
            },
          },
          {
            extend: 'excelHtml5',
            text: `<button class="btn btn-outline-primary" id="expbtns"><?= $this->load->view('svg_icons/excel.svg', [], true); ?> Excel</button>`,
            title: 'Fee Concessions report',
            footer: true,
            messageTop: '<?php echo htmlspecialchars($this->settings->getSetting('school_name')); ?> ',
            exportOptions: {
              columns: ':visible',
            },
          },
        ],
      });

      // Style the search input to match student_fees_summary_v2.php
      setTimeout(() => {
        const $input = $('.dt-search input[type="search"]');
        if ($input.length) {
          if ($input.parent().hasClass('search-box')) {
            $input.unwrap();
          }
          $input.siblings('.bi-search').remove();
          $input.addClass('input-search');
          $input.wrap('<div class="search-box position-relative"></div>');
          $input.parent().prepend('<i class="bi bi-search"></i>');
          const searchWrapper = $input.closest('.search-box');
          $('.dt-search').empty().append(searchWrapper);
          $input.off('input').on('input', function() {
            table.search(this.value).draw();
          });
        }
      }, 0);

      const $filter = $('#fee_summary_data_filter');
      const $input = $filter.find('input');

      $filter.find('label').contents().filter(function() {
        return this.nodeType === 3;
      }).remove();

      $input.attr('placeholder', 'Search');
      $filter.addClass('custom-search-box');
      $filter.find('label').wrapInner('<div class="search-box"></div>');
      $filter.find('.search-box').prepend('<i class="bi bi-search"></i>');

    }
  }

  function customPrintConcession() {
    // Get the fee summary tables
    let feeSummaryContent = '';
    const feeSummaryElem = document.querySelector('.total_summary');
    if (feeSummaryElem) {
      feeSummaryContent = feeSummaryElem.outerHTML;
    }

    // Get all data from DataTable
    let mainTableContent = '';
    if ($.fn.DataTable.isDataTable('#fee_summary_data')) {
      var table = $('#fee_summary_data').DataTable();
      var headers = $('#fee_summary_data thead').html();
      var footers = $('#fee_summary_data tfoot').length ? $('#fee_summary_data tfoot').html() : '';
      var allData = table.rows({
        search: 'applied'
      }).data();

      let rowsHtml = '';
      for (let i = 0; i < allData.length; i++) {
        rowsHtml += '<tr>';
        for (let j = 0; j < allData[i].length; j++) {
          rowsHtml += '<td>' + allData[i][j] + '</td>';
        }
        rowsHtml += '</tr>';
      }

      mainTableContent = `
        <div class="table-responsive">
          <table class="table table-bordered" style="width: 100%">
            <thead>${headers}</thead>
            <tbody>${rowsHtml}</tbody>
            ${footers ? `<tfoot>${footers}</tfoot>` : ''}
          </table>
        </div>
      `;
    } else {
      // fallback: clone the DOM (may be incomplete)
      const tableResponsiveElem = document.querySelector('.table-responsive');
      if (tableResponsiveElem && tableResponsiveElem.innerHTML.trim() !== '') {
        mainTableContent = tableResponsiveElem.outerHTML;
      }
    }

    // Get header from print_visible div
    var header = $("#print_visible").html();

    // Combine all content
    const printContent = header + '<br><br>' + feeSummaryContent + '<br><br>' + mainTableContent;

    // Open print window
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
      <html>
        <head>
          <title>Fee Concessions Report</title>
          <link rel="stylesheet" href="<?php echo base_url('assets/css/bootstrap.min.css'); ?>">
          <style>
            body {
              font-family: 'Poppins', sans-serif;
              padding: 20px;
              color: #333;
              background: #fff;
            }
            h3, h4, h5 {
              margin: 5px 0;
              text-align: center;
              font-weight: 500;
            }
            h4 {
              text-align: center;
              margin-top: 0.25rem;
              font-size: 1.3rem;
              font-weight: bold;
              letter-spacing: 1px;
              color: #222;
              text-transform: uppercase;
              border-top: 1px solid #444;
              border-bottom: 1px solid #444;
              padding: 0.5rem 0;
              font-family: 'Poppins', serif;
            }
            table {
              border-collapse: collapse !important;
              width: 100% !important;
              margin-top: 20px;
              font-size: 10pt;
              color: #333;
            }
            th, td {
              border: 1px solid #ccc !important;
              padding: 8px 12px;
              text-align: left;
              vertical-align: middle;
            }
            th {
              background-color: #f4f7fc !important;
              font-weight: 600;
              color: #333;
            }
            .table-bordered {
              width: 100% !important;
            }
            tfoot th {
              background-color: #f9f9f9;
              font-weight: 600;
            }
            @media print {
              table { page-break-inside: auto; }
              tr { page-break-inside: avoid; }
            }
          </style>
        </head>
        <body>
          ${printContent}
          <script>
            window.onload = function() {
              window.print();
            };
            window.onafterprint = function() {
              window.close();
            };
          <\/script>
        </body>
      </html>
    `);

    printWindow.document.close();
  }


  function initializeColVisButton(table, wrapperId) {
    // Add a column visibility button to the given table
    new $.fn.dataTable.Buttons(table, {
      buttons: [
        {
          extend: 'colvis',
          text: '<button class="btn btn-info"><span class="fa fa-columns" aria-hidden="true"></span> Columns</button>',
          className: 'btn btn-info',
        },
      ],
    }).container().appendTo($(`#${wrapperId} label`));
  }
</script>
<script type="text/javascript">
  function numberToCurrency(amount) {
    var formatter = new Intl.NumberFormat('en-IN', {
      // style: 'currency',
      currency: 'INR',
    });
    return formatter.format(amount);
  }

  function add_scroller(container_id) {
    var container = document.getElementById(container_id);
    if (container && container.clientWidth < container.scrollWidth) {
      $("#range-input").html(`<input type="range" class="form-control-range" id="formControlRange" oninput="scroll_report('${container_id}', this.value)" min="0" max="${container.scrollWidth - container.clientWidth}" value="0" style="width: 100%">`);
    } else {
      $("#range-input").html('');
    }
  }

  function scroll_report(container_id, scrollTo) {
    var container = document.getElementById(container_id);
    if (container) {
      var scroll_value = scrollTo - container.scrollLeft;
      container.scrollLeft += scroll_value;
    }
  }
</script>

<style>
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500&display=swap');

/* Table Styling to match student_fees_summary_v2.php */
#fee_summary_data {
  width: 100%;
  border-collapse: collapse;
  background-color: #ffffff;
  border-radius: 1.5rem;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
  opacity: 1 !important;
  transition: none !important;
  font-family: 'Poppins', sans-serif !important;
}

#fee_summary_data thead th {
  position: sticky !important;
  top: 0;
  background-color: #f1f5f9;
  color: #111827;
  font-size: 13px;
  font-weight: 500;
  z-index: 10;
  text-align: left;
  padding: 12px 16px;
}

#fee_summary_data th,
#fee_summary_data td {
  padding: 10px 14px;
  border-bottom: 1px solid #e5e7eb;
  font-size: 12px;
  font-weight: 400;
}

#fee_summary_data tbody tr:nth-child(even) {
  background-color: #f9fafb;
}

#fee_summary_data tbody tr:hover {
  background-color: #f1f5f9;
}

#fee_summary_data tfoot tr {
  background-color: #f3f4f6;
  font-weight: 500;
}

.table-responsive {
  margin-top: 20px;
}

.fee_summary {
  margin-top: 20px;
}

/* DataTable Button Styling */
.dt-button {
  border: none !important;
  background: none !important;
  margin: 0 -2px 5px -2px;
}

.btn-outline-primary {
  border-radius: 8px !important;
}

.dt-button .btn {
  line-height: 20px;
}

.dt-buttons {
  text-align: right;
  float: right;
}

/* Search Box Styling */
.search-box {
  display: flex;
  align-items: center;
  width: 260px;
  height: 40px;
  gap: 8px;
  border-radius: 4px;
  border: 1px solid #DEDCDF;
  background: #F9FAFB;
  padding: 0 14px 0 38px;
  position: relative;
  margin-right: 18px;
  margin-top: 6px;
}

.search-box:focus-within {
  border-color: #623CE7;
}

.search-box .bi-search {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 20px;
  color: #d1d1e0;
  pointer-events: none;
  transition: color 0.2s;
}

.search-box:focus-within .bi-search {
  color: #623CE7;
}

.input-search {
  font-family: "Inter", sans-serif !important;
  flex: 1;
  border: none;
  background: transparent;
  font-size: 15px;
  color: #161327;
  outline: none;
  line-height: 1.5;
  padding: 0;
}

.input-search::placeholder {
  color: #b3b3c6;
  font-size: 14px;
  font-weight: 400;
}

/* Container style */
.dataTables_filter.custom-search-box {
  float: right;
  margin-bottom: 5px;
  margin-right: -17px;
}

/* Search box */
.dataTables_filter .search-box {
  display: flex;
  align-items: center;
  width: 260px;
  height: 40px;
  gap: 8px;
  border-radius: 4px;
  border: 1px solid #DEDCDF;
  background: #F9FAFB;
  box-shadow: 0 2px 8px rgba(16, 24, 40, 0.07);
  padding: 0 14px 0 38px;
  position: relative;
  transition: box-shadow 0.2s, border-color 0.2s;
}

.dataTables_filter .search-box:focus-within {
  box-shadow: 0 4px 16px rgba(98, 60, 231, 0.10);
  border-color: #623CE7;
}

#report_width_container {
  padding-left: 30px;
  padding-right: 25px;
}
</style>